const HomePage = () => {
  const services = [
    {
      icon: Layers,
      title: "CNC Laser Cutting",
      description: "High-precision laser cutting for various materials to achieve intricate designs.",
      imageUrl:
        "https://images.unsplash.com/photo-1611288624395-97a74a1d488e?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
    {
      icon: Settings,
      title: "CNC Bending",
      description: "Accurate metal bending to your exact specifications with our modern press brakes.",
      imageUrl:
        "https://images.unsplash.com/photo-1594998741348-78483451b69a?q=80&w=1964&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
    {
      icon: Waypoints,
      title: "CNC Pipe Cutting",
      description: "Specialized pipe and tube cutting for complex shapes and structural profiles.",
      imageUrl:
        "https://images.unsplash.com/photo-1629904039743-4254b42817dc?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
    {
      icon: PaintRoller,
      title: "Powder Coating",
      description: "Durable and aesthetic powder coating finishes in a wide range of colors.",
      imageUrl:
        "https://images.unsplash.com/photo-1601679582898-99577a119f34?q=80&w=1935&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
    {
      icon: Fence,
      title: "Fabrication Works",
      description: "Comprehensive fabrication services, from expert welding to final assembly.",
      imageUrl:
        "https://images.unsplash.com/photo-1557341354-ca3a34a3b836?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
    {
      icon: Wind,
      title: "Sheet Metal Works",
      description: "Custom sheet metal fabrication including punching, shearing, and rolling.",
      imageUrl:
        "https://images.unsplash.com/photo-1629904039803-858853a15478?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
  ];

  return (
    <main>
      {/* Hero Section */}
      <section className="bg-slate-50">
        <div className="container mx-auto px-6 py-12 md:py-20 grid md:grid-cols-2 gap-10 items-center">
          <div className="text-center md:text-left">
            <h1 className="text-4xl lg:text-5xl font-extrabold text-gray-900 leading-tight mb-4">Precision CNC Machining & Fabrication</h1>
            <p className="text-base lg:text-lg text-gray-600 mb-8 max-w-md mx-auto md:mx-0">
              Delivering superior quality and unparalleled accuracy for your most demanding projects.
            </p>
            <a
              href="#"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-full text-base transition duration-300 transform hover:scale-105"
            >
              Our Services <ArrowRight className="inline-block ml-2 h-5 w-5" />
            </a>
          </div>
          <div>
            <img
              src="https://images.unsplash.com/photo-1581092448348-a73da3b4a205?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              alt="CNC Machine in operation"
              className="rounded-lg shadow-xl w-full h-auto object-cover"
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = "https://placehold.co/600x400/e2e8f0/4a5568?text=CNC+Machine";
              }}
            />
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">What We Do</h2>
            <p className="text-base text-gray-600 mt-2">A complete range of services for your fabrication needs.</p>
            <div className="inline-block w-20 h-1 bg-blue-600 rounded mt-3"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service) => (
              <div
                key={service.title}
                className="bg-white rounded-lg border border-gray-200/80 shadow-md overflow-hidden transform hover:-translate-y-1.5 transition-all duration-300 group hover:shadow-xl"
              >
                <img
                  src={service.imageUrl}
                  alt={service.title}
                  className="w-full h-44 object-cover"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = `https://placehold.co/400x260/e2e8f0/4a5568?text=${service.title.replace(" ", "+")}`;
                  }}
                />
                <div className="p-5">
                  <div className="flex items-center mb-3">
                    <div className="p-2 bg-blue-100 text-blue-600 rounded-full mr-3">
                      <LucideIcon component={service.icon} className="h-5 w-5" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-900">{service.title}</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">{service.description}</p>
                  <a href="#" className="text-sm text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                    Learn More{" "}
                    <ArrowRight className="inline-block h-4 w-4 transform -translate-y-px transition-transform group-hover:translate-x-1" />
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="bg-slate-50 py-16">
        <div className="container mx-auto px-6 grid lg:grid-cols-2 gap-12 items-center">
          <div className="order-2 lg:order-1">
            <h2 className="text-3xl font-bold text-gray-900 mb-3">Your Partner in Precision</h2>
            <p className="text-base text-gray-600 mb-6">We combine cutting-edge technology with a commitment to quality and customer service.</p>
            <ul className="space-y-4 text-base">
              <li className="flex items-start">
                <CheckCircle className="w-6 h-6 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold">Uncompromising Quality</h4>
                  <p className="text-gray-600 text-sm">Rigorous quality control ensures flawless results that meet the highest standards.</p>
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-6 h-6 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold">Advanced Technology</h4>
                  <p className="text-gray-600 text-sm">We invest in the latest CNC machinery for efficient and precise manufacturing.</p>
                </div>
              </li>
              <li className="flex items-start">
                <CheckCircle className="w-6 h-6 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold">Expert Team</h4>
                  <p className="text-gray-600 text-sm">Our skilled engineers and technicians bring decades of experience to every project.</p>
                </div>
              </li>
            </ul>
          </div>
          <div className="order-1 lg:order-2">
            <img
              src="https://images.unsplash.com/photo-1596005489234-a952e45a8a25?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              alt="Engineer inspecting a metal part"
              className="rounded-lg shadow-xl w-full h-auto object-cover"
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = "https://placehold.co/600x700/e2e8f0/4a5568?text=Quality+Inspection";
              }}
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600">
        <div className="container mx-auto px-6 py-12 text-center">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-3">Have a Project in Mind?</h2>
          <p className="text-blue-100 text-base max-w-xl mx-auto mb-6">
            Let's work together to bring your ideas to life. Contact us today for a free, no-obligation quote.
          </p>
          <a
            href="#"
            className="inline-block bg-white hover:bg-gray-100 text-blue-600 font-bold py-3 px-8 rounded-full text-base transition duration-300 transform hover:scale-105"
          >
            Get a Free Quote
          </a>
        </div>
      </section>
    </main>
  );
};
export default HomePage;
