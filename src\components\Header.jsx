const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navLinks = ["Home", "Services", "About", "Contact"];

  return (
    <header className="bg-white/90 backdrop-blur-lg sticky top-0 z-50 border-b border-gray-200">
      <div className="container mx-auto px-6 py-3 flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <ShieldCheck className="h-7 w-7 text-blue-600" />
          <span className="text-xl font-bold tracking-tight text-gray-900">Precision CNC</span>
        </div>
        <nav className="hidden md:flex items-center space-x-7">
          {navLinks.map((link) => (
            <a
              key={link}
              href="#"
              className={`text-base font-medium transition duration-300 ${link === "Home" ? "text-blue-600" : "text-gray-600 hover:text-blue-600"}`}
            >
              {link}
            </a>
          ))}
        </nav>
        <div className="hidden md:flex">
          <a href="#" className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-full text-sm transition duration-300">
            Get a Quote
          </a>
        </div>
        <div className="md:hidden">
          <button onClick={() => setIsMenuOpen(!isMenuOpen)} className="text-gray-800 focus:outline-none">
            {isMenuOpen ? <X size={26} /> : <Menu size={26} />}
          </button>
        </div>
      </div>
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-200">
          <nav className="flex flex-col items-center px-4 py-4 space-y-3">
            {navLinks.map((link) => (
              <a
                key={link}
                href="#"
                className={`w-full text-center py-2 font-medium rounded-md ${
                  link === "Home" ? "bg-blue-600 text-white" : "text-gray-600 hover:bg-blue-100"
                }`}
              >
                {link}
              </a>
            ))}
            <a
              href="#"
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-full w-full text-center mt-2 transition duration-300"
            >
              Get a Quote
            </a>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
